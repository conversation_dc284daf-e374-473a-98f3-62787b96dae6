nohup: ignoring input
INFO:     Started server process [59979]
INFO:     Waiting for application startup.
2025-08-03 16:19:35,369 - app.main - INFO - Initializing Entity Extraction API
2025-08-03 16:19:35,794 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 16:19:35,794 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 16:19:35,794 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:35,794 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:35,921 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 16:19:35,921 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 16:19:35,921 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:35,921 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:35,997 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 16:19:35,997 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 16:19:35,997 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:35,997 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:19:36,204 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:19:36,204 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 16:19:36,204 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 16:19:36,204 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,204 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,302 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 16:19:36,302 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 16:19:36,302 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,302 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,400 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 16:19:36,400 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 16:19:36,400 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,400 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,494 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 16:19:36,494 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 16:19:36,494 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,494 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,581 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 16:19:36,581 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 16:19:36,581 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,581 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,639 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 16:19:36,639 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 16:19:36,639 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,639 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,711 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 16:19:36,711 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 16:19:36,711 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,711 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:36,780 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:19:36,780 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:19:36,893 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 16:19:36,953 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:19:36,953 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:19:36,953 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 16:19:36,953 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 16:19:36,953 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:36,953 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,015 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 16:19:37,015 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 16:19:37,015 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:37,015 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,069 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 16:19:37,069 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 16:19:37,069 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:37,069 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,147 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 16:19:37,147 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 16:19:37,147 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:37,147 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,205 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 16:19:37,205 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 16:19:37,205 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:37,205 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,277 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 16:19:37,277 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 16:19:37,278 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:37,278 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,335 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 16:19:37,335 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 16:19:37,335 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 16:19:37,335 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 16:19:37,390 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:19:37,390 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:19:37,512 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
INFO:     127.0.0.1:38780 - "GET / HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38782 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38780 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:38780 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:44046 - "POST /entity-extraction/analyze HTTP/1.1" 422 Unprocessable Content
INFO:     127.0.0.1:53924 - "POST /entity-extraction/analyze HTTP/1.1" 422 Unprocessable Content
[2025-08-03 16:40:01][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83",
  "website_url": "https://www.shell.in",
  "org_id": "2"
}
2025-08-03 16:40:01,420 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:01,420 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:01,422 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:01,422 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:01,422 INFO sqlalchemy.engine.Engine [generated in 0.00036s] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:01.360018', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83",  ... (22 characters truncated) ... 03T16:40:01.360006", "data": {"scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:01,422 - sqlalchemy.engine.Engine - INFO - [generated in 0.00036s] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:01.360018', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83",  ... (22 characters truncated) ... 03T16:40:01.360006", "data": {"scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:01,481 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:01,481 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:01,650 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:01,650 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:01,653 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 16:40:01,653 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 16:40:01,653 INFO sqlalchemy.engine.Engine [generated in 0.00023s] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': '2'}
2025-08-03 16:40:01,653 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': '2'}
[2025-08-03 16:40:01][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: No existing analysis found
2025-08-03 16:40:02,223 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:02,223 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:02,224 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:02,224 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:02,224 INFO sqlalchemy.engine.Engine [cached since 0.8021s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:01.834383', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "No existing analysis found", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:01.834373", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:02,224 - sqlalchemy.engine.Engine - INFO - [cached since 0.8021s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:01.834383', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "No existing analysis found", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:01.834373", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:02,314 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:02,314 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:02,477 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 16:40:02,477 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 16:40:02,671 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:02,671 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:02,672 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 16:40:02,672 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 16:40:02,672 INFO sqlalchemy.engine.Engine [cached since 1.019s ago] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': '2'}
2025-08-03 16:40:02,672 - sqlalchemy.engine.Engine - INFO - [cached since 1.019s ago] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': '2'}
2025-08-03 16:40:02,761 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 16:40:02,761 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 16:40:02,761 INFO sqlalchemy.engine.Engine [generated in 0.00027s] {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'PENDING', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T16:40:02.759968Z', 'started_at': None, 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 16:40:02,761 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'PENDING', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T16:40:02.759968Z', 'started_at': None, 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 16:40:02,835 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:02,835 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:03,068 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:03,068 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:03,070 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 16:40:03,070 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 16:40:03,070 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'pk_1': 165}
2025-08-03 16:40:03,070 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'pk_1': 165}
[2025-08-03 16:40:03][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Created new analysis record with ID: 165
2025-08-03 16:40:03,211 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:03,211 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:03,212 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:03,212 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:03,212 INFO sqlalchemy.engine.Engine [cached since 1.79s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:03.144442', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Created new analysis record with ID: 165", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:03.144428", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:03,212 - sqlalchemy.engine.Engine - INFO - [cached since 1.79s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:03.144442', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Created new analysis record with ID: 165", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:03.144428", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:03,274 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:03,274 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:03,394 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 16:40:03,394 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 16:40:03][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Updated logger with analysis ID: 165
2025-08-03 16:40:04,051 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:04,051 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:04,052 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:04,052 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:04,052 INFO sqlalchemy.engine.Engine [cached since 2.63s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:03.752456', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 165", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:03.752445", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:04,052 - sqlalchemy.engine.Engine - INFO - [cached since 2.63s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:03.752456', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 165", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:03.752445", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:04,289 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:04,289 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:04,593 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:04,593 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:04,595 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 16:40:04,595 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 16:40:04,595 INFO sqlalchemy.engine.Engine [generated in 0.00025s] {'pk_1': 165}
2025-08-03 16:40:04,595 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] {'pk_1': 165}
2025-08-03 16:40:04,696 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 16:40:04,696 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 16:40:04,696 INFO sqlalchemy.engine.Engine [generated in 0.00025s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T16:40:04.534083', 'entity_extraction_analysis_id': 165}
2025-08-03 16:40:04,696 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T16:40:04.534083', 'entity_extraction_analysis_id': 165}
2025-08-03 16:40:04,795 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:04,795 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:04][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Updated analysis 165 status to IN_PROGRESS
2025-08-03 16:40:05,073 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:05,073 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:05,073 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:05,073 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:05,073 INFO sqlalchemy.engine.Engine [cached since 3.651s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:04.954323', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 165 status to IN_PROGRESS", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:04.954313", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:05,073 - sqlalchemy.engine.Engine - INFO - [cached since 3.651s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:04.954323', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 165 status to IN_PROGRESS", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:04.954313", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:05,267 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:05,267 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:05][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Retrieving policy URLs with reachability status
2025-08-03 16:40:05,489 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:05,489 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:05,490 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:05,490 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:05,490 INFO sqlalchemy.engine.Engine [cached since 4.068s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:05.376502', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:05.376494", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:05,490 - sqlalchemy.engine.Engine - INFO - [cached since 4.068s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:05.376502', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:05.376494", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:05,684 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:05,684 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:07][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in
2025-08-03 16:40:07,519 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:07,519 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:07,519 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:07,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:07,519 INFO sqlalchemy.engine.Engine [cached since 6.098s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:07.433244', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:07.433236", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:07,519 - sqlalchemy.engine.Engine - INFO - [cached since 6.098s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:07.433244', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:07.433236", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:07,594 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:07,594 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:08][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 16:40:08,179 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:08,179 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:08,179 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:08,179 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:08,179 INFO sqlalchemy.engine.Engine [cached since 6.757s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:08.121513', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:08.121504", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:08,179 - sqlalchemy.engine.Engine - INFO - [cached since 6.757s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:08.121513', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:08.121504", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:08,239 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:08,239 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:08][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in
2025-08-03 16:40:08,707 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:08,707 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:08,707 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:08,707 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:08,707 INFO sqlalchemy.engine.Engine [cached since 7.285s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:08.613604', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:08.613593", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:08,707 - sqlalchemy.engine.Engine - INFO - [cached since 7.285s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:08.613604', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:08.613593", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:08,785 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:08,785 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:09][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 16:40:09,836 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:09,836 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:09,837 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:09,837 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:09,837 INFO sqlalchemy.engine.Engine [cached since 8.415s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:09.774500', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:09.774491", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:09,837 - sqlalchemy.engine.Engine - INFO - [cached since 8.415s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:09.774500', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:09.774491", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:09,884 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:09,884 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:10][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 16:40:10,227 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:10,227 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:10,227 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:10,227 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:10,227 INFO sqlalchemy.engine.Engine [cached since 8.806s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:10.159790', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:10.159781", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:10,227 - sqlalchemy.engine.Engine - INFO - [cached since 8.806s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:10.159790', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:10.159781", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:10,301 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:10,301 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:10][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Total policy URLs retrieved: 5
2025-08-03 16:40:10,523 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:10,523 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:10,523 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:10,523 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:10,523 INFO sqlalchemy.engine.Engine [cached since 9.102s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:10.434393', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:10.434384", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:10,523 - sqlalchemy.engine.Engine - INFO - [cached since 9.102s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:10.434393', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:10.434384", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:10,593 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:10,593 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:11][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in
2025-08-03 16:40:11,141 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:11,141 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:11,141 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:11,141 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:11,141 INFO sqlalchemy.engine.Engine [cached since 9.72s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:11.033146', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:11.033138", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:11,141 - sqlalchemy.engine.Engine - INFO - [cached since 9.72s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:11.033146', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: 687971e6-3639-4f09-a702-e438f3f4ee83 for domain: shell.in", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:11.033138", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:11,209 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:11,209 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:12][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 16:40:12,767 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:12,767 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:12,768 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:12,768 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:12,768 INFO sqlalchemy.engine.Engine [cached since 11.35s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:12.458743', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:12.458733", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:12,768 - sqlalchemy.engine.Engine - INFO - [cached since 11.35s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:12.458743', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:12.458733", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:13,003 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:13,003 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:13][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 16:40:13,431 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:13,431 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:13,432 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:13,432 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:13,432 INFO sqlalchemy.engine.Engine [cached since 12.01s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:13.370442', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:13.370430", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:13,432 - sqlalchemy.engine.Engine - INFO - [cached since 12.01s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:13.370442', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:13.370430", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:13,525 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:13,525 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:13][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 16:40:13,712 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:13,712 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:13,712 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:13,712 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:13,712 INFO sqlalchemy.engine.Engine [cached since 12.29s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:13.644001', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:13.643994", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:13,712 - sqlalchemy.engine.Engine - INFO - [cached since 12.29s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:13.644001', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:13.643994", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:13,829 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:13,829 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:14][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 16:40:14,159 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:14,159 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:14,159 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:14,159 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:14,159 INFO sqlalchemy.engine.Engine [cached since 12.74s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:14.029392', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:14.029383", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:14,159 - sqlalchemy.engine.Engine - INFO - [cached since 12.74s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:14.029392', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:14.029383", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:14,214 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:14,214 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:14][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 16:40:14,499 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:14,499 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:14,499 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:14,499 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:14,499 INFO sqlalchemy.engine.Engine [cached since 13.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:14.392469', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:14.392461", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:14,499 - sqlalchemy.engine.Engine - INFO - [cached since 13.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:14.392469', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:14.392461", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:14,650 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:14,650 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:14][EntityExtractor][url_retrieval_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 16:40:14,819 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:14,819 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:14,820 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:14,820 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:14,820 INFO sqlalchemy.engine.Engine [cached since 13.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:14.754287', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:14.754280", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:14,820 - sqlalchemy.engine.Engine - INFO - [cached since 13.4s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T16:40:14.754287', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:14.754280", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:14,894 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:14,894 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:19,107 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 16:40:40,305 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 16:40:40,315 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 16:40:19][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 16:40:19][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 16:40:40][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=406 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=2018 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3705 traffic_type=None
[2025-08-03 16:40:40][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 901,
  "finish_reason": "STOP"
}
API response logged to: api_logs/gemini_20250803_164042_64a79242.json
[2025-08-03 16:40:42][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Gemini extraction rate 81.8% < 90%, forcing backup flow for all fields
2025-08-03 16:40:42,381 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:42,381 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:42,381 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:42,381 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:42,382 INFO sqlalchemy.engine.Engine [cached since 40.96s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:42.328986', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 81.8% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:42.328980", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:42,382 - sqlalchemy.engine.Engine - INFO - [cached since 40.96s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:42.328986', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 81.8% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:42.328980", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:42,450 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:42,450 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:42][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Gemini missing fields to fallback: ['shipping_countries', 'shipping_policy_details']
2025-08-03 16:40:42,632 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:42,632 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:42,632 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:42,632 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:42,633 INFO sqlalchemy.engine.Engine [cached since 41.21s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:42.570231', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:42.570223", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:42,633 - sqlalchemy.engine.Engine - INFO - [cached since 41.21s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:42.570231', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:42.570223", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:42,714 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:42,714 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:43][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Starting OpenAI backup flow for missing fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 16:40:43,103 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:43,103 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:43,104 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:43,104 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:43,104 INFO sqlalchemy.engine.Engine [cached since 41.68s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:43.006446', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:43.006437", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:43,104 - sqlalchemy.engine.Engine - INFO - [cached since 41.68s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:43.006446', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:43.006437", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:43,187 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:43,187 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:43,476 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:43,476 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:43,479 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 16:40:43,479 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 16:40:43,479 INFO sqlalchemy.engine.Engine [generated in 0.00044s] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83'}
2025-08-03 16:40:43,479 - sqlalchemy.engine.Engine - INFO - [generated in 0.00044s] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83'}
[2025-08-03 16:40:43][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 16:40:43,708 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:43,708 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:43,708 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:43,708 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:43,709 INFO sqlalchemy.engine.Engine [cached since 42.29s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:43.653540', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:43.653529", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:43,709 - sqlalchemy.engine.Engine - INFO - [cached since 42.29s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:43.653540', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:43.653529", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:43,782 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:43,782 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:43,943 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 16:40:43,943 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 16:40:44][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Found policy texts for 4 policy types
2025-08-03 16:40:44,240 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:44,240 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:44,240 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:44,240 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:44,240 INFO sqlalchemy.engine.Engine [cached since 42.82s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:44.172615', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:44.172607", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:44,240 - sqlalchemy.engine.Engine - INFO - [cached since 42.82s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:44.172615', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:44.172607", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:44,308 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:44,308 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:48][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Processing shipping_info with fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 16:40:48,651 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:48,651 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:48,652 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:48,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:48,652 INFO sqlalchemy.engine.Engine [cached since 47.23s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:48.595806', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:48.595798", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:48,652 - sqlalchemy.engine.Engine - INFO - [cached since 47.23s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:48.595806', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:48.595798", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:48,724 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:48,724 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:50,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 16:40:50,415 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:50,415 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:50,415 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:50,415 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:50,415 INFO sqlalchemy.engine.Engine [cached since 48.99s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 16:40:48', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 16:40:50,415 - sqlalchemy.engine.Engine - INFO - [cached since 48.99s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 16:40:48', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 16:40:50,516 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:50,516 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:50][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 16:40:50,858 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:50,858 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:50,858 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:50,858 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:50,858 INFO sqlalchemy.engine.Engine [cached since 49.44s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:50.672216', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:50.672210", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:50,858 - sqlalchemy.engine.Engine - INFO - [cached since 49.44s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:50.672216', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:50.672210", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:50,957 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:50,957 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:51][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Making final comprehensive call for remaining fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 16:40:51,130 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:51,130 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:51,130 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:51,130 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:51,130 INFO sqlalchemy.engine.Engine [cached since 49.71s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:51.065325', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:51.065317", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:51,130 - sqlalchemy.engine.Engine - INFO - [cached since 49.71s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:51.065325', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:51.065317", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:51,192 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:51,192 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:40:52,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 16:40:52,622 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:52,622 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:52,622 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:52,622 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:52,622 INFO sqlalchemy.engine.Engine [cached since 51.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 16:40:51', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 16:40:52,622 - sqlalchemy.engine.Engine - INFO - [cached since 51.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 16:40:51', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 16:40:52,761 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:52,761 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:52][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: OpenAI comprehensive call extracted 0 fields: []
2025-08-03 16:40:52,939 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:52,939 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:52,939 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:52,939 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:52,939 INFO sqlalchemy.engine.Engine [cached since 51.52s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:52.878642', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:52.878637", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:52,939 - sqlalchemy.engine.Engine - INFO - [cached since 51.52s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:52.878642', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:52.878637", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:53,052 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:53,052 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:53][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Total OpenAI backup extracted 0 fields: []
2025-08-03 16:40:53,608 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:53,608 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:53,609 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:53,609 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:53,609 INFO sqlalchemy.engine.Engine [cached since 52.19s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:53.390317', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 0 fields: []", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:53.390304", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:53,609 - sqlalchemy.engine.Engine - INFO - [cached since 52.19s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:53.390317', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 0 fields: []", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:53.390304", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:53,950 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:53,950 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:54][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: OpenAI backup completed but no additional data extracted
2025-08-03 16:40:54,368 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:54,368 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:54,368 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:54,368 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:54,368 INFO sqlalchemy.engine.Engine [cached since 52.95s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:54.274154', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup completed but no additional data extracted", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:54.274146", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:54,368 - sqlalchemy.engine.Engine - INFO - [cached since 52.95s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:54.274154', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup completed but no additional data extracted", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:54.274146", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:54,593 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:54,593 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:55][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Merging 1 AI extraction results
2025-08-03 16:40:55,530 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:55,530 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:55,530 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:55,530 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:55,530 INFO sqlalchemy.engine.Engine [cached since 54.11s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:55.331197', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 1 AI extraction results", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:55.331190", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:55,530 - sqlalchemy.engine.Engine - INFO - [cached since 54.11s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:55.331197', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 1 AI extraction results", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:55.331190", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:55,836 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:55,836 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:56][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Merged result contains 8 fields
2025-08-03 16:40:56,290 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:56,290 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:56,291 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:56,291 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:56,291 INFO sqlalchemy.engine.Engine [cached since 54.87s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:56.109342', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:56.109334", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:56,291 - sqlalchemy.engine.Engine - INFO - [cached since 54.87s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:56.109342', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:56.109334", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:56,389 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:56,389 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:56][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'support_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 16:40:57,183 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:57,183 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:57,184 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:57,184 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:57,184 INFO sqlalchemy.engine.Engine [cached since 55.76s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:56.966425', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_numbers ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:56.966417", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:57,184 - sqlalchemy.engine.Engine - INFO - [cached since 55.76s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:56.966425', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_numbers ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:56.966417", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:57,379 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:57,379 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:57][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 16:40:57,549 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:57,549 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:57,549 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:57,549 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:57,549 INFO sqlalchemy.engine.Engine [cached since 56.13s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:57.492252', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:57.492242", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:57,549 - sqlalchemy.engine.Engine - INFO - [cached since 56.13s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:57.492252', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:57.492242", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:57,629 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:57,629 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:57][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 16:40:57,837 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:57,837 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:57,837 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:57,837 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:57,837 INFO sqlalchemy.engine.Engine [cached since 56.42s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:57.775154', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:57.775146", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:57,837 - sqlalchemy.engine.Engine - INFO - [cached since 56.42s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:57.775154', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:57.775146", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:57,932 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:57,932 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:58][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 16:40:58,320 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:58,320 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:58,320 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:58,320 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:58,320 INFO sqlalchemy.engine.Engine [cached since 56.9s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:58.196257', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:58.196249", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:58,320 - sqlalchemy.engine.Engine - INFO - [cached since 56.9s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:58.196257', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:58.196249", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:58,449 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:58,449 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:58][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 16:40:58,773 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:58,773 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:58,773 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:58,773 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:58,773 INFO sqlalchemy.engine.Engine [cached since 57.35s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:58.689088', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:58.689081", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:58,773 - sqlalchemy.engine.Engine - INFO - [cached since 57.35s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:58.689088', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:58.689081", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:58,884 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:58,884 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:40:59][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Before database storage - jurisdiction_details: The website mentions a 'Jurisdiction' section in its terms and conditions. [2]
2025-08-03 16:40:59,567 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:40:59,567 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:40:59,567 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:59,567 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:40:59,567 INFO sqlalchemy.engine.Engine [cached since 58.15s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:59.476312', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: The website mentions a \'Jurisdiction\' section in its terms and conditions. [2]", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:59.476304", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:59,567 - sqlalchemy.engine.Engine - INFO - [cached since 58.15s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:40:59.476312', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: The website mentions a \'Jurisdiction\' section in its terms and conditions. [2]", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:40:59.476304", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:40:59,678 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:40:59,678 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:41:00][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Before database storage - jurisdiction_place: ['India']
2025-08-03 16:41:00,839 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:00,839 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:00,840 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:00,840 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:00,840 INFO sqlalchemy.engine.Engine [cached since 59.42s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:00.275195', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_place: [\'India\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:00.275188", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:00,840 - sqlalchemy.engine.Engine - INFO - [cached since 59.42s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:00.275195', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_place: [\'India\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:00.275188", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:01,418 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:01,418 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:41:02][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Before database storage - has_jurisdiction_law: True
2025-08-03 16:41:02,228 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:02,228 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:02,228 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:02,228 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:02,228 INFO sqlalchemy.engine.Engine [cached since 60.81s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:02.018354', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - has_jurisdiction_law: True", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:02.018345", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:02,228 - sqlalchemy.engine.Engine - INFO - [cached since 60.81s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:02.018354', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - has_jurisdiction_law: True", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:02.018345", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:02,316 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:02,316 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 16:41:02,788 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:02,788 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:02,788 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 16:41:02,788 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 16:41:02,788 INFO sqlalchemy.engine.Engine [cached since 60.03s ago] {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': ['<EMAIL>'], 'support_email': ['<EMAIL>'], 'business_contact_numbers': ['044-3099 1103', '044-4344 2650', '+91 44 46945101'], 'business_location': ['2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India', 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100, India'], 'has_jurisdiction_law': True, 'jurisdiction_details': "The website mentions a 'Jurisdiction' section in its terms and conditions. [2]", 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': ['India'], 'shipping_countries': '', 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T16:41:02.523478', 'started_at': '2025-08-03T16:41:02.523463', 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 16:41:02,788 - sqlalchemy.engine.Engine - INFO - [cached since 60.03s ago] {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': ['<EMAIL>'], 'support_email': ['<EMAIL>'], 'business_contact_numbers': ['044-3099 1103', '044-4344 2650', '+91 44 46945101'], 'business_location': ['2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India', 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100, India'], 'has_jurisdiction_law': True, 'jurisdiction_details': "The website mentions a 'Jurisdiction' section in its terms and conditions. [2]", 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': ['India'], 'shipping_countries': '', 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T16:41:02.523478', 'started_at': '2025-08-03T16:41:02.523463', 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 16:41:03,030 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 16:41:03,030 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 16:41:03][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] ERROR: Error storing analysis results: (pymysql.err.OperationalError) (1241, 'Operand should contain 1 column(s)')
[SQL: INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)]
[parameters: {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': ['<EMAIL>'], 'support_email': ['<EMAIL>'], 'business_contact_numbers': ['044-3099 1103', '044-4344 2650', '+91 44 46945101'], 'business_location': ['2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096, India', 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100, India'], 'has_jurisdiction_law': True, 'jurisdiction_details': "The website mentions a 'Jurisdiction' section in its terms and conditions. [2]", 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': ['India'], 'shipping_countries': '', 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T16:41:02.523478', 'started_at': '2025-08-03T16:41:02.523463', 'completed_at': None, 'error_message': None, 'org_id': '2'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-03 16:41:03,570 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:03,570 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:03,570 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:03,570 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:03,571 INFO sqlalchemy.engine.Engine [cached since 62.15s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:03.272051', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error storing analysis results: (pymysql.err.OperationalError) (1241, \'Operand should contain 1 column(s)\')\\n[SQL:  ... (2963 characters truncated) ... ps://sqlalche.me/e/20/e3q8)", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:03.272040", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:03,571 - sqlalchemy.engine.Engine - INFO - [cached since 62.15s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:03.272051', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error storing analysis results: (pymysql.err.OperationalError) (1241, \'Operand should contain 1 column(s)\')\\n[SQL:  ... (2963 characters truncated) ... ps://sqlalche.me/e/20/e3q8)", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:03.272040", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:03,810 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:03,810 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:41:04][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Creating response with merged_result keys: ['legal_name', 'business_email', 'support_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'privacy_policy_text', 'terms_conditions_text']
2025-08-03 16:41:04,991 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:04,991 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:04,991 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:04,991 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:04,991 INFO sqlalchemy.engine.Engine [cached since 63.57s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:04.372726', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_n ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:04.372715", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:04,991 - sqlalchemy.engine.Engine - INFO - [cached since 63.57s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:04.372726', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_n ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:04.372715", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:05,494 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:05,494 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:41:05][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Response jurisdiction_details: The website mentions a 'Jurisdiction' section in its terms and conditions. [2]
2025-08-03 16:41:05,986 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:05,986 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:05,986 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:05,986 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:05,986 INFO sqlalchemy.engine.Engine [cached since 64.56s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:05.761785', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: The website mentions a \'Jurisdiction\' section in its terms and conditions. [2]", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:05.761777", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:05,986 - sqlalchemy.engine.Engine - INFO - [cached since 64.56s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:05.761785', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: The website mentions a \'Jurisdiction\' section in its terms and conditions. [2]", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:05.761777", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:06,318 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:06,318 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:41:06][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Response jurisdiction_place: ['India']
2025-08-03 16:41:07,096 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:07,096 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:07,097 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:07,097 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:07,097 INFO sqlalchemy.engine.Engine [cached since 65.68s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:06.726326', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:06.726317", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:07,097 - sqlalchemy.engine.Engine - INFO - [cached since 65.68s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:06.726326', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:06.726317", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:07,594 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:07,594 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 16:41:08][EntityExtractor][165][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Response has_jurisdiction_law: yes
2025-08-03 16:41:08,718 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 16:41:08,718 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 16:41:08,718 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:08,718 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 16:41:08,719 INFO sqlalchemy.engine.Engine [cached since 67.3s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:08.388458', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:08.388446", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:08,719 - sqlalchemy.engine.Engine - INFO - [cached since 67.3s ago] {'analysis_id': 165, 'timestamp': '2025-08-03T16:41:08.388458', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T16:41:08.388446", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 16:41:09,105 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 16:41:09,105 - sqlalchemy.engine.Engine - INFO - COMMIT
INFO:     127.0.0.1:60624 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-08-03 16:47:00,104 - app.main - INFO - Entity Extraction API shutting down
INFO:     Application shutdown complete.
INFO:     Finished server process [59979]
