from typing import Optional, List, Union, Any, Dict
from pydantic import BaseModel, Field


class EntityExtractionRequest(BaseModel):
    """
    Request model for entity extraction analysis
    """
    scrape_request_ref_id: str = Field(..., description="Reference ID for the scrape request")
    website_url: str = Field(..., description="Main website URL")
    org_id: str = Field(default="default", description="Organization ID")

    # Optional parameters for processing control
    force_reprocess: bool = Field(default=False, description="Force reprocessing even if results exist")
    use_openai_fallback: bool = Field(default=True, description="Use OpenAI as fallback when <PERSON> fails")

    class Config:
        json_schema_extra = {
            "example": {
                "scrape_request_ref_id": "req_123456789",
                "website_url": "https://example.com",
                "org_id": "default",
                "force_reprocess": False,
                "use_openai_fallback": True
            }
        }


class SimpleEntityExtractionRequest(BaseModel):
    """
    Simplified request model - just needs main website URL
    """
    website_url: str = Field(..., description="Main website URL")
    org_id: str = Field(default="default", description="Organization ID")

    # Optional parameters
    force_reprocess: bool = Field(default=True, description="Force reprocessing even if results exist")
    use_openai_fallback: bool = Field(default=True, description="Use OpenAI as fallback when Gemini fails")

    class Config:
        json_schema_extra = {
            "example": {
                "website_url": "https://example.com",
                "org_id": "default",
                "force_reprocess": False,
                "use_openai_fallback": True
            }
        }


class CombinedEntityExtractionResponse(BaseModel):
    """
    Response model for combined entity extraction (Gemini + OpenAI)
    """
    success: bool = Field(..., description="Whether the extraction was successful")
    website_url: str = Field(..., description="Website URL that was analyzed")
    scrape_request_ref_id: str = Field(..., description="Reference ID for the scrape request")
    timestamp: str = Field(..., description="Timestamp of the extraction")

    # Results from each API
    gemini_result: Dict[str, Any] = Field(..., description="Results from Gemini API")
    openai_result: Dict[str, Any] = Field(..., description="Results from OpenAI API")
    combined_result: Dict[str, Any] = Field(..., description="Final combined results")

    # Metadata
    field_sources: Dict[str, str] = Field(..., description="Source of each field (gemini/openai/not_found)")
    missing_fields_count: int = Field(..., description="Number of fields that were missing from Gemini")
    total_fields_extracted: int = Field(..., description="Total number of fields successfully extracted")

    # Optional error information
    error: Optional[str] = Field(None, description="Error message if extraction failed")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "website_url": "https://example.com",
                "scrape_request_ref_id": "req_123",
                "timestamp": "2024-01-01T12:00:00",
                "gemini_result": {"legal_name": "Example Corp", "business_email": ""},
                "openai_result": {"business_email": "<EMAIL>"},
                "combined_result": {"legal_name": "Example Corp", "business_email": "<EMAIL>"},
                "field_sources": {"legal_name": "gemini", "business_email": "openai"},
                "missing_fields_count": 1,
                "total_fields_extracted": 2
            }
        }


class EntityExtractionResponse(BaseModel):
    """
    Response model for entity extraction results
    """
    analysis_id: int
    scrape_request_ref_id: str
    website_url: str
    processing_status: str
    
    # Entity extraction results
    legal_name: Optional[str] = None
    business_email: Optional[str] = None
    support_email: Optional[str] = None
    business_contact_numbers: Optional[str] = None
    business_location: Optional[str] = None
    
    # Policy analysis results
    has_jurisdiction_law: Optional[str] = None  # "yes", "no", "unclear"
    jurisdiction_details: Optional[str] = None
    accepts_international_orders: Optional[str] = None  # "yes", "no", "unclear"
    shipping_policy_details: Optional[str] = None
    # jurisdiction_place: Optional[str] = None
    # shipping_countries: Optional[str] = None
    jurisdiction_place: Optional[Union[str, List[str]]] = None
    shipping_countries: Optional[Union[str, List[str]]] = None

    # Policy text content
    #privacy_policy_text: Optional[str] = None
    #terms_conditions_text: Optional[str] = None
    
    # Processing metadata
    extraction_method: Optional[str] = None
    urls_processed: Optional[List[str]] = None
    error_message: Optional[str] = None
    
    # Timestamps
    created_at: str
    completed_at: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "analysis_id": 123,
                "scrape_request_ref_id": "req_123456789",
                "website_url": "https://example.com",
                "processing_status": "COMPLETED",
                "legal_name": "Example Corp Ltd",
                "business_email": "<EMAIL>, <EMAIL>",
                "support_email": "<EMAIL>, <EMAIL>",
                "business_contact_numbers": "+1-555-0123, +1-555-0124",
                "business_location": "123 Main St, City, Country, 456 Branch St, City, Country",
                "has_jurisdiction_law": "yes",
                "jurisdiction_details": "Governed by laws of California, USA",
                "accepts_international_orders": "yes",
                "shipping_policy_details": "Ships worldwide with restrictions",
                "privacy_policy_text": "We collect personal information when you...",
                "terms_conditions_text": "By using our service, you agree to...",
                "extraction_method": "gemini",
                "urls_processed": ["https://example.com", "https://example.com/terms"],
                "jurisdiction_plcae": [],
                "jurisdiction_countries": [],
                "shipping_countries": [],
                "created_at": "2024-01-01T00:00:00Z",
                "completed_at": "2024-01-01T00:05:00Z"
            }
        }
